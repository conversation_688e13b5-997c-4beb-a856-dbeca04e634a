[build-system]
requires = ["maturin>=1.4,<2.0"]
build-backend = "maturin"

[tool.maturin]
features = ["pyo3/extension-module"]
python-source = "python"
module-name = "scsnv._scsnv"

[project]
name = "scsnv"
authors = [
    {name = "<PERSON><PERSON>", email = "<EMAIL>"}
]
maintainers = [
    {name = "<PERSON><PERSON>", email = "<EMAIL>"}
]
description = "scsnv"
readme = "README.md"
license = {file = "LICENSE"}
classifiers = [
    "Programming Language :: Rust",
    "Programming Language :: Python :: Implementation :: CPython",
    "Programming Language :: Python :: Implementation :: PyPy",
    "License :: OSI Approved :: MIT License",
]
requires-python = ">=3.9, <3.14"
dynamic = ["version"]

dependencies = [
    'anndata >= 0.8.0, < 0.11.0',
    'polars >= 1.0, < 2.0',
    'pooch >= 1.6.0, < 2.0.0',
    'pyfaidx >= 0.7.0, < 0.8.0',
]

[project.urls]

[project.optional-dependencies]

test = ["pytest", "hypothesis==6.72.4"]
