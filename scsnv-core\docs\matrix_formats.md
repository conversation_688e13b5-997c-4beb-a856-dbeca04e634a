# Matrix Transformation Guide for Arena Bytes

This document explains how to transform `arena_bytes` (encoded SNV mutation data) into various matrix formats suitable for different analysis workflows.

## Overview

The `arena_bytes` in `ArenaCompactSNV<'arena>` contains ultra-compact encoded SNV mutation data. This data can be transformed into several matrix formats depending on your analysis needs:

1. **Sparse COO Matrix** - For large-scale genomics analysis
2. **Dense Matrix** - For small datasets or when full data access is needed
3. **Count Matrix** - Memory-efficient format storing only mutation counts
4. **Binary Matrix** - Presence/absence of mutations
5. **AnnData Format** - Compatible with single-cell analysis tools
6. **Mutation Type Matrix** - Categorizes mutations by type

## Matrix Formats

### 1. Sparse COO (Coordinate) Matrix

**Use Case**: Large-scale genomics analysis where most genomic positions have no mutations.

**Format**: `(row_indices, col_indices, values)`
- `row_indices`: Barcode indices
- `col_indices`: Genomic position indices  
- `values`: Encoded mutation bytes

**Memory**: Very efficient for sparse data (typical in genomics)

```rust
let (row_indices, col_indices, values) = processor.to_sparse_coo_matrix(&encoded_snvs_by_barcode)?;
```

### 2. Dense Matrix

**Use Case**: Small datasets, debugging, or when you need random access to all positions.

**Format**: `Vec<Vec<Vec<u8>>>` where `matrix[barcode][position] = mutation_bytes`

**Memory**: High memory usage (barcode_count × total_positions × bytes_per_mutation)

```rust
let dense_matrix = processor.to_dense_matrix(&encoded_snvs_by_barcode)?;
```

### 3. Count Matrix

**Use Case**: When you only need mutation counts, not the full mutation data.

**Format**: `Vec<Vec<u8>>` where `matrix[barcode][position] = count`

**Memory**: Moderate (barcode_count × total_positions × 1 byte)

```rust
let count_matrix = processor.to_count_matrix(&encoded_snvs_by_barcode)?;
```

### 4. Binary Matrix

**Use Case**: Presence/absence analysis, clustering based on mutation patterns.

**Format**: `Vec<Vec<bool>>` where `matrix[barcode][position] = has_mutation`

**Memory**: Low (barcode_count × total_positions × 1 bit)

```rust
let binary_matrix = processor.to_binary_matrix(&encoded_snvs_by_barcode)?;
```

### 5. AnnData Compatible Format

**Use Case**: Integration with single-cell analysis pipelines (Scanpy, Seurat, etc.).

**Format**: `AnnDataMatrixFormat` struct with:
- `barcodes`: Cell identifiers
- `positions`: Genomic positions
- Sparse matrix data in COO format
- Metadata for easy conversion to AnnData objects

```rust
let anndata_format = processor.to_anndata_format(&encoded_snvs_by_barcode)?;
```

### 6. Mutation Type Matrix

**Use Case**: Analysis focusing on mutation types rather than specific sequences.

**Format**: `Vec<Vec<u8>>` where values represent:
- `0`: No mutation
- `1`: Substitution
- `2`: Deletion  
- `3`: Insertion

```rust
let type_matrix = processor.to_mutation_type_matrix(&encoded_snvs_by_barcode)?;
```

## High-Level Transformation API

Use `transform_to_matrix()` with `MatrixTransformOptions` for flexible transformations:

```rust
let options = MatrixTransformOptions {
    use_sparse: true,                    // Use sparse format
    include_mutation_data: false,        // Only counts, not full data
    filter_empty_positions: true,        // Remove positions with no mutations
    genome_index: None,                  // Use default human genome
};

let result = processor.transform_to_matrix(&encoded_snvs_by_barcode, options)?;
```

## Memory Considerations

| Format | Memory Usage | Best For |
|--------|-------------|----------|
| Sparse COO | Very Low | Large genomes, sparse mutations |
| Dense Full | Very High | Small datasets, debugging |
| Count Matrix | Medium | Statistical analysis |
| Binary Matrix | Low | Pattern analysis |
| AnnData | Low-Medium | Single-cell workflows |
| Type Matrix | Low | Mutation type analysis |

## Performance Tips

1. **Use sparse formats** for typical genomics data (>99% positions have no mutations)
2. **Filter empty positions** when possible to reduce matrix size
3. **Use count matrices** instead of full mutation data when sequence details aren't needed
4. **Batch process** large datasets to manage memory usage
5. **Consider the genome index** - custom indices can reduce matrix dimensions

## Integration Examples

### With AnnData (Python)
```python
# After getting AnnDataMatrixFormat from Rust
import anndata as ad
import scipy.sparse as sp

# Convert to scipy sparse matrix
sparse_matrix = sp.coo_matrix(
    (count_data, (row_indices, col_indices)), 
    shape=(num_barcodes, num_positions)
)

# Create AnnData object
adata = ad.AnnData(
    X=sparse_matrix,
    obs=pd.DataFrame(index=barcodes),
    var=pd.DataFrame(index=position_names)
)
```

### With Polars/Arrow
```rust
// Convert to Polars DataFrame
let df = df! [
    "barcode" => barcodes,
    "position" => positions,
    "count" => counts,
]?;
```

## Testing Matrix Transformations

Run the comprehensive test suite:

```bash
cargo test --lib decode::tests::test_matrix_transformations
```

Run the example:

```bash
cargo run --example matrix_transformations
```

## Custom Genome Indices

You can provide custom chromosome sizes for non-human genomes:

```rust
use scsnv_core::genome::create_genome_index;
use std::collections::HashMap;

let custom_sizes = HashMap::from([
    ("scaffold1".to_string(), 1000000),
    ("scaffold2".to_string(), 2000000),
]);

let custom_index = create_genome_index(custom_sizes);

let options = MatrixTransformOptions {
    genome_index: Some(custom_index),
    ..Default::default()
};
```
