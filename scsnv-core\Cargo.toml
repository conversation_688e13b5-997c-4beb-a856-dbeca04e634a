[package]
name = "scsnv-core"
version = "0.1.0"
edition = "2021"

[dependencies]
anyhow = "1.0"
zstd = { version = "0.13", features = ["zstdmt"] }
bed-utils = "0.9.1"
smallvec = "1.11"
flate2 = "1.0"
bincode = "2.0"
noodles = { version = "0.98", features = ["core", "sam"] }
bstr = "1.12"
lexical = "7.0"
rayon = "1.10"
itertools = "0.14"
indicatif = "0.17"
anndata = { git = "https://github.com/kaizhang/anndata-rs.git", rev = "0d27ac475634ed7703ab1a23e01e6a3031a28541"}
# Performance-oriented crates
bitvec = "1.0"
varint = "0.9"
bumpalo = "3.14"
wide = "0.7"
heapless = "0.8"
indexmap = "2.6"
num = "0.4"


[dev-dependencies]
tempfile = "3.0"
criterion = "0.5"