use crate::snv::{SNV, SNVs, Mutation};
use anyhow::{Result, bail};
use bitvec::prelude::*;
use smallvec::SmallVec;
use crate::genome::GenomeBaseIndex;
// Note: varint crate API simplified for this implementation
use bumpalo::Bump;
use std::cmp::Ordering;

/// Ultra-compact SNV encoding using advanced bit manipulation and SIMD
/// Optimized for both space efficiency and performance
#[derive(Debug, Clone, PartialEq, Eq)]
pub struct UltraCompactSNV {
    /// Bit-packed data using bitvec for precise control
    data: BitVec<u8, bitvec::order::Msb0>,
}


/// Arena-allocated version for zero-copy operations
#[derive(Debug,Clone)]
pub struct ArenaCompactSNV<'arena> {
    data: &'arena [u8],
    _phantom: std::marker::PhantomData<&'arena ()>,
}

/// SIMD-optimized batch processor for multiple SNVs
pub struct SNVProcessor {
    arena: Bump,
    batch_size: usize,
}

/// Processed SNVs with encoded data and barcode information
#[derive(Debug)]
pub struct ProcessedSNVs<'arena> {
    pub barcode: &'arena str,
    pub encoded_snvs: Vec<EncodedSNV<'arena>>,
}

/// Encoded SNV containing position, chromosome and compact mutation data
#[derive(Debug)]
pub struct EncodedSNV<'arena> {
    pub chrom: &'arena str,
    pub position: u64,
    pub count: u8,
    pub compact_mutation: u64,
}

/// Small stack-allocated vector for bases (avoids heap allocation for small insertions)
type SmallBases = SmallVec<[u8; 8]>;

impl TryFrom<&Mutation> for UltraCompactSNV {
    type Error = anyhow::Error;
    fn try_from(mutation: &Mutation) -> Result<Self, Self::Error> {
        match mutation {
            Mutation::Substitution(alt_base) => {
                let ref_base = b'A'; // here placeholder, latter may find from genome
                UltraCompactSNV::encode_substitution_fast(ref_base, *alt_base)
            }
            Mutation::Deletion(length) => {
                UltraCompactSNV::encode_deletion_optimized(*length as usize)
            }
            Mutation::Insertion(bases) => {
                UltraCompactSNV::encode_insertion_optimized(bases)
            }
        }
    }
}

impl UltraCompactSNV {
    // Operation types (2 bits) - same as before but with constants for bitvec
    const SUBSTITUTION: u8 = 0b00;
    const DELETION: u8     = 0b01;
    const INSERTION: u8    = 0b10;
    const RESERVED: u8     = 0b11;
    
    // Base encoding (2 bits each) - optimized bit patterns
    const BASE_A: [bool; 2] = [false, false]; // 00
    const BASE_T: [bool; 2] = [false, true];  // 01
    const BASE_C: [bool; 2] = [true, false];  // 10
    const BASE_G: [bool; 2] = [true, true];   // 11
    
    // Static lookup tables for fast base conversion
    const BASE_TO_U8_LOOKUP: [u8; 256] = {
        let mut lookup = [255u8; 256]; // 255 = invalid
        lookup[b'A' as usize] = 0;
        lookup[b'a' as usize] = 0;
        lookup[b'T' as usize] = 1;
        lookup[b't' as usize] = 1;
        lookup[b'C' as usize] = 2;
        lookup[b'c' as usize] = 2;
        lookup[b'G' as usize] = 3;
        lookup[b'g' as usize] = 3;
        lookup
    };
    
    const U8_TO_BASE_LOOKUP: [u8; 4] = [b'A', b'T', b'C', b'G'];
    
    /// Create from raw bit vector
    pub fn from_bits(data: BitVec<u8, bitvec::order::Msb0>) -> Self {
        Self { data }
    }
    
    /// Get the underlying bit vector
    pub fn as_bits(&self) -> &BitVec<u8, bitvec::order::Msb0> {
        &self.data
    }
    
    /// Convert to bytes for storage
    pub fn to_bytes(&self) -> Vec<u8> {
        self.data.as_raw_slice().to_vec()
    }
    
    /// Create from bytes (reconstructs bitvec)
    pub fn from_bytes(bytes: &[u8], bit_len: usize) -> Self {
        let mut data = BitVec::from_slice(bytes);
        data.truncate(bit_len);
        Self { data }
    }
    
    fn encode_substitution_byte(ref_base: u8, alt_base: u8) -> Result<u8> {
        // Convert bases to their 2-bit representation (0-3)
        let ref_u8 = Self::base_to_u8(ref_base)?;
        let alt_u8 = Self::base_to_u8(alt_base)?;

        // Construct the byte in a single expression
        // [OpType: 2 bits] [Ref: 2 bits] [Alt: 2 bits] [Padding: 2 bits]
        let byte = (Self::SUBSTITUTION << 6) | (ref_u8 << 4) | (alt_u8 << 2);
        Ok(byte)
    }

    pub fn encode_substitution_fast(ref_base: u8, alt_base: u8) -> Result<Self> {
        let byte = Self::encode_substitution_byte(ref_base, alt_base)?;
        Ok(Self { data: BitVec::from_element(byte) })
    }
    
    /// Optimized deletion encoding with varint
    pub fn encode_deletion_optimized(length: usize) -> Result<Self> {
        if length == 0 {
            bail!("Deletion length cannot be zero");
        }
        
        let mut data = BitVec::new();
        
        // Operation type
        data.extend([false, true]); // DELETION = 01
        
        if length <= 15 {
            // Small deletion: pack in 6 remaining bits
            data.push(false); // Extension flag = 0
            data.push(false); // Padding
            
            // 4-bit length
            for i in (0..4).rev() {
                data.push((length >> i) & 1 == 1);
            }
        } else {
            // Large deletion: use varint
            data.push(true); // Extension flag = 1
            data.push(false); // Padding
            
            let length_bytes = (length as u16).to_le_bytes();
            
            // Add length bytes to bit vector
            for byte in length_bytes {
                for i in (0..8).rev() {
                    data.push((byte >> i) & 1 == 1);
                }
            }
        }
        
        // Pad to byte boundary
        while data.len() % 8 != 0 {
            data.push(false);
        }
        
        Ok(Self { data })
    }
    
    /// Optimized insertion encoding with stack allocation for small insertions
    pub fn encode_insertion_optimized(bases: &[u8]) -> Result<Self> {
        if bases.is_empty() {
            bail!("Insertion cannot be empty");
        }
        
        let mut data = BitVec::new();
        
        // Operation type (2 bits)
        data.extend([true, false]); // INSERTION = 10
        
        if bases.len() <= 2 {
            // Short format: exactly 8 bits
            data.push(false); // Extension flag = 0 (short format)
            
            // Count flag (1 bit): 0 = single base, 1 = two bases
            data.push(bases.len() == 2);
            
            // First base (2 bits)
            data.extend(Self::base_to_bits(bases[0])?);
            
            // Second base (2 bits) - use A (00) as padding if only 1 base
            if bases.len() == 2 {
                data.extend(Self::base_to_bits(bases[1])?);
            } else {
                data.extend([false, false]); // Padding with A (00)
            }
            
            // Result: exactly 8 bits, no padding needed
        } else {
            // Long format: variable length with extension
            data.push(true); // Extension flag = 1 (extended format)
            
            // Reserve 1 bit for future use or additional flags
            data.push(false);
            
            // Encode length as compact as possible
            if bases.len() <= 15 {
                // 4-bit length for lengths 3-15
                data.push(false); // Length format flag: 0 = 4-bit
                let len_bits = (bases.len() as u8) & 0x0F;
                for i in (0..4).rev() {
                    data.push((len_bits >> i) & 1 == 1);
                }
            } else {
                // 8-bit length for lengths 16-255
                data.push(true); // Length format flag: 1 = 8-bit
                let len_bits = bases.len() as u8;
                for i in (0..8).rev() {
                    data.push((len_bits >> i) & 1 == 1);
                }
            }
            
            // Encode bases (2 bits each)
            for &base in bases {
                data.extend(Self::base_to_bits(base)?);
            }
            
            // Pad to byte boundary
            while data.len() % 8 != 0 {
                data.push(false);
            }
        }
        
        Ok(Self { data })
    }
    
    /// Fast substitution decoding
    fn decode_substitution_fast(&self) -> Result<Mutation> {
        if self.data.len() < 8 {
            bail!("Invalid substitution format");
        }
        
        // Extract alternative base (bits 4-5)
        let alt_bits = &self.data[4..6];
        let alt_base = Self::bits_to_base(alt_bits)?;
        
        Ok(Mutation::Substitution(alt_base))
    }
    

    /// Decode directly from u64
    pub fn decode_from_u64(value: u64) -> Result<Mutation> {
        // Extract operation type from top 2 bits
        let operation = (value >> 62) as u8;

        match operation {
            0b00 => Self::decode_substitution_from_u64(value),
            0b01 => Self::decode_deletion_from_u64(value),
            0b10 => Self::decode_insertion_from_u64(value),
            _ => bail!("Invalid operation type in u64: {}", operation),
        }
    }

    /// Decode substitution from u64
    fn decode_substitution_from_u64(value: u64) -> Result<Mutation> {
        // Extract ref_base from bits 60-61
        let _ref_bits = ((value >> 60) & 0b11) as u8;
        // Extract alt_base from bits 58-59
        let alt_bits = ((value >> 58) & 0b11) as u8;

        let alt_base = Self::u8_to_base(alt_bits)?;
        Ok(Mutation::Substitution(alt_base))
    }

    /// Decode deletion from u64
    fn decode_deletion_from_u64(value: u64) -> Result<Mutation> {
        // Extract length from bits 32-61 (30 bits)
        let length = ((value >> 32) & 0x3FFFFFFF) as usize;

        if length == 0 {
            bail!("Invalid deletion length: 0");
        }

        // Cap at u8::MAX for compatibility with existing Mutation::Deletion
        let capped_length = length.min(u8::MAX as usize) as u8;
        Ok(Mutation::Deletion(capped_length))
    }

    /// Decode insertion from u64
    fn decode_insertion_from_u64(value: u64) -> Result<Mutation> {
        // Extract length from bits 56-61 (6 bits, max 63 bases)
        let length = ((value >> 56) & 0x3F) as usize;

        if length == 0 {
            bail!("Invalid insertion length: 0");
        }

        let mut bases = Vec::with_capacity(length);

        // Extract bases from remaining bits (2 bits per base)
        for i in 0..length.min(28) { // Max 28 bases in 56 bits
            let base_bits = ((value >> (54 - i * 2)) & 0b11) as u8;
            let base = Self::u8_to_base(base_bits)?;
            bases.push(base);
        }

        Ok(Mutation::Insertion(bases))
    }
    
    /// Convert 2-bit encoding back to base
    fn u8_to_base(bits: u8) -> Result<u8> {
        match bits {
            0 => Ok(b'A'),
            1 => Ok(b'T'),
            2 => Ok(b'C'),
            3 => Ok(b'G'),
            _ => bail!("Invalid base bits: {}", bits),
        }
    }

    /// Convert BitVec data to u64 (for backward compatibility)
    pub fn to_u64(&self) -> u64 {
        let mut result = 0u64;
        let bytes = self.data.as_raw_slice();

        // Convert up to 8 bytes to u64
        for (i, &byte) in bytes.iter().enumerate().take(8) {
            result |= (byte as u64) << (i * 8);
        }

        result
    }

    /// Create UltraCompactSNV from u64
    pub fn from_u64(value: u64) -> Self {
        let bytes = value.to_le_bytes();
        let data = BitVec::from_slice(&bytes);
        Self { data }
    }

    /// Convert base character to 2-bit representation using fast lookup
    #[inline(always)]
    fn base_to_bits(base: u8) -> Result<[bool; 2]> {
        let value = Self::base_to_u8(base)?;
        Ok(match value {
            0 => Self::BASE_A,
            1 => Self::BASE_T,
            2 => Self::BASE_C,
            3 => Self::BASE_G,
            _ => unreachable!(),
        })
    }
    
    /// Convert base character to u8 using fast lookup table
    #[inline(always)]
    fn base_to_u8(base: u8) -> Result<u8> {
        let result = Self::BASE_TO_U8_LOOKUP[base as usize];
        if result == 255 {
            bail!("Invalid base: {}", base as char);
        }
        Ok(result)
    }
    
    /// Convert 2 bits back to base character using fast lookup
    #[inline(always)]
    fn bits_to_base(bits: &BitSlice<u8, bitvec::order::Msb0>) -> Result<u8> {
        if bits.len() != 2 {
            bail!("Expected 2 bits for base");
        }
        
        let value = match (bits[0], bits[1]) {
            (false, false) => 0, // A
            (false, true) => 1,  // T
            (true, false) => 2,  // C
            (true, true) => 3,   // G
        };
        
        Ok(Self::U8_TO_BASE_LOOKUP[value])
    }
    
    /// Get size in bits
    pub fn bit_size(&self) -> usize {
        self.data.len()
    }
    
    /// Get size in bytes (rounded up)
    pub fn byte_size(&self) -> usize {
        (self.data.len() + 7) / 8
    }
    
    /// Check mutation type without full decoding
    pub fn mutation_type(&self) -> Result<u8> {
        if self.data.len() < 2 {
            bail!("Invalid SNV data");
        }
        
        let op_bits = &self.data[0..2];
        Ok(if !op_bits[0] && !op_bits[1] {
            Self::SUBSTITUTION
        } else if !op_bits[0] && op_bits[1] {
            Self::DELETION
        } else if op_bits[0] && !op_bits[1] {
            Self::INSERTION
        } else {
            Self::RESERVED
        })
    }
}

/// Arena-based allocator for zero-copy SNV operations
impl SNVProcessor {
    /// Create new processor with specified batch size
    pub fn new(batch_size: usize) -> Self {
        Self {
            arena: Bump::new(),
            batch_size,
        }
    }
    /// Process SNVs structure with zero-copy allocation
    pub fn process_snvs<'a>(&'a self, snvs: Vec<SNV>) -> Result<Vec<EncodedSNV<'a>>> {
        let mut encoded_snvs = Vec::with_capacity(snvs.len());
        
        for chunk in snvs.chunks(self.batch_size) {
            for snv in chunk {
                let encoded = self.encode_snv_in_arena(snv)?;
                encoded_snvs.push(encoded);
            }
        }
        Ok(encoded_snvs)
    }
    
    /// Encode mutation using arena allocation
    fn encode_in_arena<'a>(&'a self, mutation: &Mutation) -> Result<ArenaCompactSNV<'a>> {
        let compact = UltraCompactSNV::try_from(mutation)?; 
        
        let bytes = compact.to_bytes();
        let arena_bytes = self.arena.alloc_slice_copy(&bytes);
        
        Ok(ArenaCompactSNV {
            data: arena_bytes,
            _phantom: std::marker::PhantomData,
        })
    }
    
    /// Encode SNV using arena allocation
    fn encode_snv_in_arena<'a>(&'a self, snv: &SNV) -> Result<EncodedSNV<'a>> {
        let compact_mutation_data = self.encode_in_arena(&snv.mutation_type)?;
        
        // Convert the compact mutation data to u64
        let compact_mutation = bytes_to_u64(compact_mutation_data.data);
        
        // Allocate chromosome string in arena
        let chrom = self.arena.alloc_str(&snv.chrom);
        
        Ok(EncodedSNV {
            chrom,
            position: snv.position,
            count: snv.count,
            compact_mutation,
        })
    }
    // make Vec<EncodedSNV> to a tuple (pos, value)
    pub fn to_arraydata<V>(&self, encoded_snvs: &Vec<EncodedSNV>) -> Result<Vec<(usize, V)>>
    where
        V: TryFrom<u64> + Ord,
        <V as TryFrom<u64>>::Error: std::fmt::Debug,
    {
        let mut arraydata = Vec::new();
        let genome_index = GenomeBaseIndex::create_human_genome_index();
        for encoded_snv in encoded_snvs {
            let chrom = encoded_snv.chrom;
            let position = encoded_snv.position;
            let corrected_position = genome_index.get_position_rev(chrom, position);
            let mutation_byte = encoded_snv.compact_mutation;
            let value = mutation_byte.try_into().expect(
                format!(
                    "cannot convert mutation_byte {} to {}",
                    mutation_byte,
                    std::any::type_name::<V>()
                )
                .as_str(),
            );
            arraydata.push((corrected_position, value));
        }
        
        Ok(arraydata)
    }
    
    /// Reset arena for reuse
    pub fn reset(&mut self) {
        self.arena.reset();
    }
    
    /// Get memory usage statistics
    pub fn memory_usage(&self) -> usize {
        self.arena.allocated_bytes()
    }
}

impl<'arena> ArenaCompactSNV<'arena> {
    /// Get the raw data
    pub fn as_bytes(&self) -> &[u8] {
        self.data
    }
    
    /// Convert back to UltraCompactSNV for decoding
    pub fn to_ultra_compact(&self) -> UltraCompactSNV {
        UltraCompactSNV::from_bytes(self.data, self.data.len() * 8)
    }
}

impl<'arena> ProcessedSNVs<'arena> {
    /// Get the barcode
    pub fn barcode(&self) -> &str {
        self.barcode
    }
    
    /// Get the encoded SNVs
    pub fn encoded_snvs(&self) -> &[EncodedSNV<'arena>] {
        &self.encoded_snvs
    }
    
    /// Get the number of SNVs
    pub fn len(&self) -> usize {
        self.encoded_snvs.len()
    }
    
    /// Check if empty
    pub fn is_empty(&self) -> bool {
        self.encoded_snvs.is_empty()
    }
    
    /// Decode all mutations back to original format
    pub fn decode_all(&self) -> Result<Vec<(String, u64, Mutation, u8)>> {
        let mut results = Vec::with_capacity(self.encoded_snvs.len());
        
        for encoded_snv in &self.encoded_snvs {
            let mutation = UltraCompactSNV::decode_from_u64(encoded_snv.compact_mutation)?;
            results.push((
                encoded_snv.chrom.to_string(),
                encoded_snv.position,
                mutation,
                encoded_snv.count,
            ));
        }
        
        Ok(results)
    }
}

impl<'arena> EncodedSNV<'arena> {
    /// Get the chromosome
    pub fn chrom(&self) -> &str {
        self.chrom
    }
    
    /// Get the position
    pub fn position(&self) -> u64 {
        self.position
    }
    
    /// Get the count
    pub fn count(&self) -> u8 {
        self.count
    }
    
    /// Get the compact mutation
    pub fn compact_mutation(&self) -> &u64 {
        &self.compact_mutation
    }
    
    /// Decode the mutation
    pub fn decode_mutation(&self) -> Result<Mutation> {
        UltraCompactSNV::decode_from_u64(self.compact_mutation)
    }
    
    /// Convert back to SNV
    pub fn to_snv(&self) -> Result<SNV> {
        let mutation = self.decode_mutation()?;
        Ok(SNV::new(
            self.chrom.to_string(),
            self.position,
            mutation,
            self.count,
        ))
    }
}

fn bytes_to_u64(bytes: &[u8]) -> u64 {
    match bytes.len() {
        0 => 0,
        1 => bytes[0] as u64,
        2 => u16::from_le_bytes(bytes.try_into().unwrap()) as u64,
        3..=4 => {
            let mut padded = [0u8; 4];
            padded[..bytes.len()].copy_from_slice(bytes);
            u32::from_le_bytes(padded) as u64
        },
        _ => {
            bytes.iter().fold(0u64, |acc, &b| 
                acc.wrapping_mul(256).wrapping_add(b as u64))
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_space_efficiency() -> Result<()> {
        let original = SNV::new(
            "chr1".to_string(),
            12345,
            Mutation::Substitution(b'T'),
            1
        );
        
        let ultra = UltraCompactSNV::encode_substitution_fast(b'A', b'T')?;
        
        println!("Original SNV: ~40+ bytes estimated");
        println!("Ultra compact: {} bits ({} bytes)", ultra.bit_size(), ultra.byte_size());
        
        // Should be exactly 1 byte for substitution
        assert_eq!(ultra.byte_size(), 1);
        
        Ok(())
    }
    
    #[test]
    fn test_mutation_type_check() -> Result<()> {
        let sub = UltraCompactSNV::encode_substitution_fast(b'A', b'T')?;
        let del = UltraCompactSNV::encode_deletion_optimized(5)?;
        let ins = UltraCompactSNV::encode_insertion_optimized(b"AT")?;
        
        assert_eq!(sub.mutation_type()?, UltraCompactSNV::SUBSTITUTION);
        assert_eq!(del.mutation_type()?, UltraCompactSNV::DELETION);
        assert_eq!(ins.mutation_type()?, UltraCompactSNV::INSERTION);
        
        Ok(())
    }
    

    #[test]
    fn test_encoded_snv_methods() -> Result<()> {
        let processor = SNVProcessor::new(100);
        let snv = SNV::new("chr1".to_string(), 12345, Mutation::Substitution(b'G'), 3);
        let encoded = processor.encode_snv_in_arena(&snv)?;
        
        assert_eq!(encoded.chrom(), "chr1");
        assert_eq!(encoded.position(), 12345);
        assert_eq!(encoded.count(), 3);
        
        let decoded_mutation = encoded.decode_mutation()?;
        assert_eq!(decoded_mutation, Mutation::Substitution(b'G'));
        
        let reconstructed_snv = encoded.to_snv()?;
        assert_eq!(reconstructed_snv.chrom, "chr1");
        assert_eq!(reconstructed_snv.position, 12345);
        assert_eq!(reconstructed_snv.mutation_type, Mutation::Substitution(b'G'));
        assert_eq!(reconstructed_snv.count, 3);
        
        Ok(())
    }
}
