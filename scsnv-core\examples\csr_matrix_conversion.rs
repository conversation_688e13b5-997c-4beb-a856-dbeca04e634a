use scsnv_core::quantification::{
    mutations_to_numeric_csr, create_count_csr_matrix, 
    mutation_bytes_to_numeric, mutation_bytes_to_type
};
use scsnv_core::genome::GenomeBaseIndex;
use scsnv_core::fragment::Fragment;
use anyhow::Result;

/// Example demonstrating how to convert mutation data to CSR matrix format
fn main() -> Result<()> {
    println!("=== CSR Matrix Conversion Examples ===\n");
    
    // Create sample fragments
    let fragments = create_sample_fragments();
    let genome_index = GenomeBaseIndex::create_human_genome_index();
    
    // Example 1: Convert to f64 CSR matrix
    println!("1. Converting to f64 CSR Matrix:");
    let (barcodes, row_indices, col_indices, values_f64) = 
        mutations_to_numeric_csr(fragments.clone(), &genome_index)?;
    
    println!("   - Barcodes: {:?}", barcodes);
    println!("   - Matrix shape: {} x {}", barcodes.len(), genome_index.len());
    println!("   - Non-zero entries: {}", row_indices.len());
    println!("   - Sample values (f64): {:?}", &values_f64[..values_f64.len().min(5)]);
    
    // Example 2: Convert to i32 CSR matrix (for count data)
    println!("\n2. Converting to i32 Count Matrix:");
    let (barcodes_i32, row_indices_i32, col_indices_i32, values_i32) = 
        create_count_csr_matrix(fragments.clone(), &genome_index)?;
    
    println!("   - Barcodes: {:?}", barcodes_i32);
    println!("   - Non-zero entries: {}", row_indices_i32.len());
    println!("   - Sample values (i32): {:?}", &values_i32[..values_i32.len().min(5)]);
    
    // Example 3: Working with mutation bytes directly
    println!("\n3. Converting Mutation Bytes to Numbers:");
    let sample_bytes = vec![
        vec![0b00000100], // Substitution: A->T
        vec![0b01000000], // Deletion: length 0 (encoded differently)
        vec![0b10000000], // Insertion
    ];
    
    for (i, bytes) in sample_bytes.iter().enumerate() {
        let numeric_value = mutation_bytes_to_numeric(bytes);
        let mutation_type = mutation_bytes_to_type(bytes);
        println!("   - Bytes {:?} -> Numeric: {}, Type: {}", bytes, numeric_value, mutation_type);
    }
    
    // Example 4: Create CSR matrix compatible with nalgebra-sparse
    println!("\n4. nalgebra-sparse Compatible Format:");
    create_nalgebra_sparse_example(&barcodes, &row_indices, &col_indices, &values_f64)?;
    
    // Example 5: Create CSR matrix compatible with sprs crate
    println!("\n5. sprs Crate Compatible Format:");
    create_sprs_example(&barcodes, &row_indices, &col_indices, &values_f64)?;
    
    Ok(())
}

fn create_sample_fragments() -> Vec<Fragment> {
    let mut fragments = Vec::new();
    
    // Create fragments with different barcodes
    for (i, barcode) in ["AAACCTGAGAAACCAT", "AAACCTGAGAAACCGC", "AAACCTGAGAAACCTT"].iter().enumerate() {
        let mut fragment = Fragment::new("chr1", 1000 + i as u64 * 1000, 2000 + i as u64 * 1000);
        fragment.barcode = Some(barcode.to_string());
        fragments.push(fragment);
    }
    
    fragments
}

/// Example using nalgebra-sparse (if available)
fn create_nalgebra_sparse_example(
    barcodes: &[String],
    row_indices: &[usize],
    col_indices: &[usize], 
    values: &[f64]
) -> Result<()> {
    println!("   Creating nalgebra-sparse CSR matrix:");
    println!("   - Rows (barcodes): {}", barcodes.len());
    println!("   - Cols (positions): estimated from max col index");
    println!("   - Non-zeros: {}", values.len());
    
    if !values.is_empty() {
        let max_col = col_indices.iter().max().unwrap_or(&0);
        println!("   - Matrix dimensions: {} x {}", barcodes.len(), max_col + 1);
        
        // Example code for nalgebra-sparse (commented out since it's not in dependencies)
        /*
        use nalgebra_sparse::CsrMatrix;
        
        let matrix = CsrMatrix::try_from_csr_data(
            barcodes.len(),
            max_col + 1,
            row_offsets,  // You need to compute row offsets from row_indices
            col_indices.to_vec(),
            values.to_vec()
        )?;
        */
    }
    
    Ok(())
}

/// Example using sprs crate (if available)
fn create_sprs_example(
    barcodes: &[String],
    row_indices: &[usize],
    col_indices: &[usize],
    values: &[f64]
) -> Result<()> {
    println!("   Creating sprs CSR matrix:");
    println!("   - Format: COO -> CSR conversion");
    println!("   - Data ready for sprs::CsMat::new()");
    
    if !values.is_empty() {
        let max_col = col_indices.iter().max().unwrap_or(&0);
        println!("   - Matrix shape: ({}, {})", barcodes.len(), max_col + 1);
        
        // Example code for sprs (commented out since it's not in dependencies)
        /*
        use sprs::CsMat;
        
        let matrix = CsMat::new(
            (barcodes.len(), max_col + 1),
            row_indices.to_vec(),
            col_indices.to_vec(),
            values.to_vec()
        );
        */
    }
    
    Ok(())
}

/// Helper function to convert COO format to CSR format
pub fn coo_to_csr(
    num_rows: usize,
    row_indices: &[usize],
    col_indices: &[usize],
    values: &[f64]
) -> (Vec<usize>, Vec<usize>, Vec<f64>) {
    // Sort by row, then by column
    let mut triplets: Vec<(usize, usize, f64)> = row_indices
        .iter()
        .zip(col_indices.iter())
        .zip(values.iter())
        .map(|((&r, &c), &v)| (r, c, v))
        .collect();
    
    triplets.sort_by(|a, b| a.0.cmp(&b.0).then(a.1.cmp(&b.1)));
    
    // Build CSR format
    let mut row_offsets = vec![0; num_rows + 1];
    let mut csr_col_indices = Vec::new();
    let mut csr_values = Vec::new();
    
    let mut current_row = 0;
    for (row, col, val) in triplets {
        // Fill row_offsets for empty rows
        while current_row < row {
            current_row += 1;
            row_offsets[current_row] = csr_col_indices.len();
        }
        
        csr_col_indices.push(col);
        csr_values.push(val);
    }
    
    // Fill remaining row_offsets
    while current_row < num_rows {
        current_row += 1;
        row_offsets[current_row] = csr_col_indices.len();
    }
    
    (row_offsets, csr_col_indices, csr_values)
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_csr_conversion() -> Result<()> {
        let fragments = create_sample_fragments();
        let genome_index = GenomeBaseIndex::create_human_genome_index();
        
        let (barcodes, row_indices, col_indices, values) = 
            mutations_to_numeric_csr(fragments, &genome_index)?;
        
        // Basic validation
        assert_eq!(row_indices.len(), col_indices.len());
        assert_eq!(col_indices.len(), values.len());
        assert!(barcodes.len() > 0);
        
        // Test COO to CSR conversion
        if !values.is_empty() {
            let (row_offsets, csr_cols, csr_vals) = coo_to_csr(
                barcodes.len(),
                &row_indices,
                &col_indices,
                &values
            );
            
            assert_eq!(row_offsets.len(), barcodes.len() + 1);
            assert_eq!(csr_cols.len(), csr_vals.len());
        }
        
        Ok(())
    }
    
    #[test]
    fn test_mutation_bytes_conversion() {
        // Test substitution
        let sub_bytes = vec![0b00000100]; // Substitution A->T
        assert_eq!(mutation_bytes_to_type(&sub_bytes), 1.0);
        
        // Test deletion
        let del_bytes = vec![0b01000000]; // Deletion
        assert_eq!(mutation_bytes_to_type(&del_bytes), 2.0);
        
        // Test insertion
        let ins_bytes = vec![0b10000000]; // Insertion
        assert_eq!(mutation_bytes_to_type(&ins_bytes), 3.0);
        
        // Test empty
        assert_eq!(mutation_bytes_to_type(&[]), 0.0);
    }
}
