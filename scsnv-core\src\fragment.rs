use bed_utils::bed::{B<PERSON><PERSON><PERSON>, Strand, ParseError};

pub type CellBarcode = String;
#[derive(Debu<PERSON>, <PERSON>lone)]
pub struct Fragment {
    pub chrom: String,
    pub start: u64,
    pub end: u64,
    pub barcode: Option<CellBarcode>,
    pub count: u32,
    pub strand: Option<Strand>,
    pub start1 : Option<u64>,
    pub snv1 : Option<String>,
    pub start2 : Option<u64>,
    pub snv2 : Option<String>,
}

impl Fragment {
    pub fn new(chrom: impl Into<String>, start: u64, end: u64) -> Self {
        Self {
            chrom: chrom.into(),
            start,
            end,
            barcode: None,
            count: 1,
            strand: None,
            start1: None,
            snv1: None,
            start2: None,
            snv2: None,
        }
    }

    pub fn is_all_matched(&self) -> bool {
        let check_numeric = |s: &str| s.bytes().all(|b| b.is_ascii_digit());
        self.snv1.as_ref().map_or(true, |s| check_numeric(s)) &&
        self.snv2.as_ref().map_or(true, |s| check_numeric(s))
    }
}

impl core::fmt::Display for Fragment {
    fn fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result {
        write!(
            f,
            "{}\t{}\t{}\t{}\t{}",
            self.chrom(),
            self.start(),
            self.end(),
            self.barcode.as_deref().unwrap_or("."),
            self.count,
        )?;
        if let Some(strand) = self.strand() {
            write!(f, "\t{}", strand)?;
        }
        Ok(())
    }
}

impl std::str::FromStr for Fragment {
    type Err = ParseError;

    fn from_str(s: &str) -> Result<Self, Self::Err> {
        let mut fields = s.split('\t');
        let chrom = fields
            .next()
            .ok_or(ParseError::MissingReferenceSequenceName)?
            .to_string();
        let start = fields
            .next()
            .ok_or(ParseError::MissingStartPosition)
            .and_then(|s| lexical::parse(s).map_err(ParseError::InvalidStartPosition))?;
        let end = fields
            .next()
            .ok_or(ParseError::MissingEndPosition)
            .and_then(|s| lexical::parse(s).map_err(ParseError::InvalidEndPosition))?;
        let barcode = fields
            .next()
            .ok_or(ParseError::MissingName)
            .map(|s| match s {
                "." => None,
                _ => Some(s.into()),
            })?;
        let count = fields.next().map_or(Ok(1), |s| {
            if s == "." {
                Ok(1)
            } else {
                lexical::parse(s).map_err(ParseError::InvalidStartPosition)
            }
        })?;
        let strand = fields.next().map_or(Ok(None), |s| {
            if s == "." {
                Ok(None)
            } else {
                s.parse().map(Some).map_err(ParseError::InvalidStrand)
            }
        })?;
        let start1 = fields.next().map_or(Ok(None), |s| {
            if s == "." {
                Ok(None)
            } else {
                lexical::parse(s).map(Some).map_err(ParseError::InvalidStartPosition)
            }
        })?;
        let snv1 = fields.next().map_or(Ok(None), |s| { 
            if s == "." {
                Ok(None)
            } else {
                Ok(Some(s.to_string()))
            }
        })?;
        let start2 = fields.next().map_or(Ok(None), |s| {
            if s == "." {
                Ok(None)
            } else {
                lexical::parse(s).map(Some).map_err(ParseError::InvalidStartPosition)
            }
        })?;
        let snv2 = fields.next().map_or(Ok(None), |s| {
            if s == "." {
                Ok(None)
            } else {
                Ok(Some(s.to_string()))
            }
        })?;
        Ok(Fragment {
            chrom,
            start,
            end,
            barcode,
            count,
            strand,
            start1,
            snv1,
            start2,
            snv2,
        })
    }

}
impl BEDLike for Fragment {
    fn chrom(&self) -> &str {
        &self.chrom
    }
    fn set_chrom(&mut self, chrom: &str) -> &mut Self {
        self.chrom = chrom.to_string();
        self
    }
    fn start(&self) -> u64 {
        self.start
    }
    fn set_start(&mut self, start: u64) -> &mut Self {
        self.start = start;
        self
    }
    fn end(&self) -> u64 {
        self.end
    }
    fn set_end(&mut self, end: u64) -> &mut Self {
        self.end = end;
        self
    }
    fn name(&self) -> Option<&str> {
        self.barcode.as_deref()
    }
    fn score(&self) -> Option<bed_utils::bed::Score> {
        Some(self.count.try_into().unwrap())
    }
    fn strand(&self) -> Option<Strand> {
        self.strand
    }
}

