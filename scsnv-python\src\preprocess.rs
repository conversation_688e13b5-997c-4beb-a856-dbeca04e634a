/* 
use anndata::{
    data::{ArrayChunk, DataFrameIndex, DynCsrMatrix},
    AnnDataOp, ArrayData, AxisArraysOp,
};

#[pyfunction]
#[pyo3(signature = (
    anndata, fragment_file, chrom_size, mitochondrial_dna, min_num_fragment,
    fragment_is_sorted_by_name, shift_left, shift_right, chunk_size, white_list=None, tempdir=None
))]
pub(crate) fn import_fragments(
    anndata: AnnDataLike,
    fragment_file: PathBuf,
    chrom_size: BTreeMap<String, u64>,
    mitochondrial_dna: Vec<String>,
    min_num_fragment: u64,
    fragment_is_sorted_by_name: bool,
    shift_left: i64,
    shift_right: i64,
    chunk_size: usize,
    white_list: Option<HashSet<String>>,
    tempdir: Option<PathBuf>,
) -> Result<()> {
    let mitochondrial_dna: HashSet<String> = mitochondrial_dna.into_iter().collect();
    let final_white_list = if fragment_is_sorted_by_name || min_num_fragment <= 0 {
        white_list
    } else {
        let mut barcode_count = preprocessing::get_barcode_count(
            bed::io::Reader::new(
                utils::open_file_for_read(&fragment_file),
                Some("#".to_string()),
            )
            .into_records()
            .map(Result::unwrap),
        );
        let list: HashSet<String> = barcode_count
            .drain()
            .filter_map(|(k, v)| if v >= min_num_fragment { Some(k) } else { None })
            .collect();
        match white_list {
            None => Some(list),
            Some(x) => Some(list.intersection(&x).map(Clone::clone).collect()),
        }
    };
    let chrom_sizes = chrom_size.into_iter().collect();
    let fragments = bed::io::Reader::new(
        utils::open_file_for_read(&fragment_file),
        Some("#".to_string()),
    )
    .into_records()
    .map(|f| {
        let mut f = f.unwrap();
        shift_fragment(&mut f, shift_left, shift_right);
        f
    });
    let sorted_fragments: Box<dyn Iterator<Item = Fragment>> = if !fragment_is_sorted_by_name {
        let mut sorter = ExternalSorterBuilder::new()
            .with_chunk_size(50000000)
            .with_compression(2);
        if let Some(tmp) = tempdir {
            sorter = sorter.with_tmp_dir(tmp);
        }
        Box::new(
            sorter
                .build()
                .unwrap()
                .sort_by(fragments, |a, b| a.barcode.cmp(&b.barcode))
                .unwrap()
                .map(Result::unwrap),
        )
    } else {
        Box::new(fragments)
    };

    macro_rules! run {
        ($data:expr) => {
            preprocessing::import_fragments(
                $data,
                sorted_fragments,
                &mitochondrial_dna,
                &chrom_sizes,
                final_white_list.as_ref(),
                min_num_fragment,
                chunk_size,
            )?
        };
    }

    crate::with_anndata!(&anndata, run);
    Ok(())
}
    */