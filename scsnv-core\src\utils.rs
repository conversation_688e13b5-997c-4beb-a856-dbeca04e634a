use std::path::Path;
use std::fs::File;
use std::str::FromStr;
use flate2::read::MultiGzDecoder;
use zstd::stream::read::Decoder;


#[derive(Debug, <PERSON><PERSON>, Co<PERSON>)]
pub enum Compression {
    Gzip,
    Zstd,
}

impl FromStr for Compression {
    type Err = String;

    fn from_str(s: &str) -> Result<Self, Self::Err> {
        match s.to_lowercase().as_str() {
            "gzip" => Ok(Compression::Gzip),
            "zstd" | "zstandard" => Ok(Compression::Zstd),
            _ => Err(format!("unsupported compression: {}", s)),
        }
    }
}
/// Directly copied from snapatac2
/// Determine the file compression type. Supports gzip and zstd.
fn detect_compression<P: AsRef<Path>>(file: P) -> Option<Compression> {
    if MultiGzDecoder::new(File::open(file.as_ref()).unwrap()).header().is_some() {
        Some(Compression::Gzip)
    } else if let Some(ext) = file.as_ref().extension() {
        if ext == "zst" {
            Some(Compression::Zstd)
        } else {
            None
        }
    } else {
        None
    }
}


/// Open a file, possibly compressed. Supports gzip and zstd.
pub fn open_file_for_read<P: AsRef<Path>>(file: P) -> Box<dyn std::io::Read> {
    match detect_compression(file.as_ref()) {
        Some(Compression::Gzip) => Box::new(MultiGzDecoder::new(File::open(file.as_ref()).unwrap())),
        Some(Compression::Zstd) => {
            let r = Decoder::new(File::open(file.as_ref()).unwrap()).unwrap();
            Box::new(r)
        },
        None => Box::new(File::open(file.as_ref()).unwrap()),
    }
}