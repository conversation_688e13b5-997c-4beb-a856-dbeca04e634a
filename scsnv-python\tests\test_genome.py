import pytest
import tempfile
import os
from pathlib import Path
from scsnv.genome import Genome

def create_test_fasta(filename, chrom_sizes):
    """Create a simple test FASTA file with specified chromosome sizes."""
    with open(filename, 'w') as f:
        for chrom, size in chrom_sizes.items():
            f.write(f">{chrom}\n")
            # Write a sequence of 'A's with the specified size
            f.write('A' * size + '\n')

def test_genome_init_with_paths():
    """Test creating a Genome object with direct Path objects."""
    with tempfile.TemporaryDirectory() as tmpdir:
        # Create test files
        fasta_path = os.path.join(tmpdir, "test.fasta")
        gff_path = os.path.join(tmpdir, "test.gff")
        
        # Create a simple FASTA file
        create_test_fasta(fasta_path, {"chr1": 1000, "chr2": 2000})
        
        # Create a simple GFF file
        with open(gff_path, 'w') as f:
            f.write("# Test GFF file\n")
        
        # Create Genome object
        genome = Genome(
            fasta=Path(fasta_path),
            annotation=Path(gff_path)
        )
        
        # Test that paths are stored correctly
        assert genome.fasta == Path(fasta_path)
        assert genome.annotation == Path(gff_path)

def test_genome_init_with_strings():
    """Test creating a Genome object with string paths."""
    with tempfile.TemporaryDirectory() as tmpdir:
        # Create test files
        fasta_path = os.path.join(tmpdir, "test.fasta")
        gff_path = os.path.join(tmpdir, "test.gff")
        
        # Create a simple FASTA file
        create_test_fasta(fasta_path, {"chr1": 1000, "chr2": 2000})
        
        # Create a simple GFF file
        with open(gff_path, 'w') as f:
            f.write("# Test GFF file\n")
        
        # Create Genome object
        genome = Genome(
            fasta=fasta_path,
            annotation=gff_path
        )
        
        # Test that paths are stored correctly
        assert str(genome.fasta) == fasta_path
        assert str(genome.annotation) == gff_path

def test_genome_init_with_callables():
    """Test creating a Genome object with callable paths."""
    with tempfile.TemporaryDirectory() as tmpdir:
        # Create test files
        fasta_path = os.path.join(tmpdir, "test.fasta")
        gff_path = os.path.join(tmpdir, "test.gff")
        
        # Create a simple FASTA file
        create_test_fasta(fasta_path, {"chr1": 1000, "chr2": 2000})
        
        # Create a simple GFF file
        with open(gff_path, 'w') as f:
            f.write("# Test GFF file\n")
        
        # Create callables that return paths
        def get_fasta():
            return Path(fasta_path)
        
        def get_annotation():
            return Path(gff_path)
        
        # Create Genome object
        genome = Genome(
            fasta=get_fasta,
            annotation=get_annotation
        )
        
        # Test that paths are fetched correctly
        assert genome.fasta == Path(fasta_path)
        assert genome.annotation == Path(gff_path)

def test_genome_with_predefined_chrom_sizes():
    """Test creating a Genome object with predefined chromosome sizes."""
    with tempfile.TemporaryDirectory() as tmpdir:
        # Create test files
        fasta_path = os.path.join(tmpdir, "test.fasta")
        gff_path = os.path.join(tmpdir, "test.gff")
        
        # Create empty test files
        open(fasta_path, 'w').close()
        open(gff_path, 'w').close()
        
        # Define chromosome sizes
        chrom_sizes = {"chr1": 1000, "chr2": 2000, "chr3": 3000}
        
        # Create Genome object
        genome = Genome(
            fasta=Path(fasta_path),
            annotation=Path(gff_path),
            chrom_sizes=chrom_sizes
        )
        
        # Test that chromosome sizes are stored correctly
        assert genome.chrom_sizes == chrom_sizes

def test_genome_infer_chrom_sizes(monkeypatch):
    """Test that chromosome sizes are inferred from FASTA file when not provided."""
    with tempfile.TemporaryDirectory() as tmpdir:
        # Create test files
        fasta_path = os.path.join(tmpdir, "test.fasta")
        gff_path = os.path.join(tmpdir, "test.gff")
        
        # Create a simple FASTA file with known chromosome sizes
        test_chrom_sizes = {"chr1": 1000, "chr2": 2000, "chr3": 3000}
        create_test_fasta(fasta_path, test_chrom_sizes)
        
        # Create a simple GFF file
        with open(gff_path, 'w') as f:
            f.write("# Test GFF file\n")
        
        # Create Genome object
        genome = Genome(
            fasta=Path(fasta_path),
            annotation=Path(gff_path)
        )
        
        # Since we're in a test environment, pyfaidx might not be available
        # Let's mock the Fasta class to return our test chromosome sizes
        class MockFasta:
            def __init__(self, path):
                self.path = path
                self.chromosomes = list(test_chrom_sizes.keys())
            
            def __getitem__(self, key):
                # Return an object with a len() method that returns the chromosome size
                class Chromosome:
                    def __init__(self, size):
                        self.size = size
                    
                    def __len__(self):
                        return self.size
                
                return Chromosome(test_chrom_sizes[key])
            
            def keys(self):
                return self.chromosomes
        
        # Mock the pyfaidx import
        #monkeypatch.setattr("scsnv.genome.Fasta", MockFasta)
        
        # Test that chromosome sizes are inferred correctly
        inferred_sizes = genome.chrom_sizes
        assert inferred_sizes == test_chrom_sizes

def test_predefined_genomes():
    """Test that predefined genome objects exist and have the expected structure."""
    import scsnv.genome as genome_module
    
    # Test that predefined genomes exist
    assert hasattr(genome_module, 'GRCh37')
    assert hasattr(genome_module, 'hg19')
    assert hasattr(genome_module, 'GRCh38')
    assert hasattr(genome_module, 'hg38')
    assert hasattr(genome_module, 'GRCm39')
    assert hasattr(genome_module, 'mm39')
    assert hasattr(genome_module, 'GRCm38')
    assert hasattr(genome_module, 'mm10')
    
    # Test that hg19 is an alias for GRCh37
    assert genome_module.hg19 is genome_module.GRCh37
    
    # Test that hg38 is an alias for GRCh38
    assert genome_module.hg38 is genome_module.GRCh38
    
    # Test that mm39 is an alias for GRCm39
    assert genome_module.mm39 is genome_module.GRCm39
    
    # Test that mm10 is an alias for GRCm38
    assert genome_module.mm10 is genome_module.GRCm38

def test_predefined_genome_chrom_sizes():
    """Test that predefined genomes have chromosome sizes."""
    import scsnv.genome as genome_module
    
    # Test that predefined genomes have chromosome sizes
    assert genome_module.hg38.chrom_sizes is not None
    assert genome_module.mm39.chrom_sizes is not None
    
    # Check that human genome has expected chromosomes
    hg38_chroms = genome_module.hg38.chrom_sizes
    assert "chr1" in hg38_chroms
    assert "chrX" in hg38_chroms
    assert "chrY" in hg38_chroms
    assert "chrM" in hg38_chroms
    
    # Check that mouse genome has expected chromosomes
    mm39_chroms = genome_module.mm39.chrom_sizes
    assert "chr1" in mm39_chroms
    assert "chrX" in mm39_chroms
    assert "chrY" in mm39_chroms
    assert "chrM" in mm39_chroms

if __name__ == "__main__":
    pytest.main([__file__])