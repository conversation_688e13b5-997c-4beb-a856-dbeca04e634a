use scsnv_core::decode::{UltraCompactSNV, SNVProcessor, EncodedSNV};
use scsnv_core::snv::{SNV, Mutation};
use anyhow::Result;

/// Example demonstrating the new u64 encoding/decoding system
fn main() -> Result<()> {
    println!("=== u64 SNV Encoding/Decoding Examples ===\n");
    
    // Example 1: Direct u64 encoding and decoding
    println!("1. Direct u64 Encoding/Decoding:");
    test_direct_u64_encoding()?;
    
    // Example 2: Using SNVProcessor with u64 output
    println!("\n2. SNVProcessor with u64 Output:");
    test_snv_processor_u64()?;
    
    // Example 3: Converting between formats
    println!("\n3. Format Conversion:");
    test_format_conversion()?;
    
    // Example 4: Performance comparison
    println!("\n4. Performance Benefits:");
    demonstrate_performance_benefits()?;
    
    Ok(())
}

fn test_direct_u64_encoding() -> Result<()> {
    // Test substitution
    let sub_u64 = encode_substitution_to_u64(b'A', b'T')?;
    let decoded_sub = UltraCompactSNV::decode_from_u64(sub_u64)?;
    println!("   Substitution A->T: u64={:016x}, decoded={:?}", sub_u64, decoded_sub);
    
    // Test deletion
    let del_u64 = encode_deletion_to_u64(5)?;
    let decoded_del = UltraCompactSNV::decode_from_u64(del_u64)?;
    println!("   Deletion length=5: u64={:016x}, decoded={:?}", del_u64, decoded_del);
    
    // Test insertion
    let ins_u64 = encode_insertion_to_u64(&[b'A', b'T', b'G'])?;
    let decoded_ins = UltraCompactSNV::decode_from_u64(ins_u64)?;
    println!("   Insertion ATG: u64={:016x}, decoded={:?}", ins_u64, decoded_ins);
    
    Ok(())
}

fn test_snv_processor_u64() -> Result<()> {
    let processor = SNVProcessor::new(100);
    
    // Create test SNVs
    let snvs = vec![
        SNV::new("chr1".to_string(), 1000, Mutation::Substitution(b'G'), 2),
        SNV::new("chr1".to_string(), 2000, Mutation::Deletion(3), 1),
        SNV::new("chr2".to_string(), 500, Mutation::Insertion(vec![b'A', b'T']), 1),
    ];
    
    // Process SNVs (this should now produce u64 values)
    let encoded_snvs = processor.process_snvs(snvs)?;
    
    // Get array data with u64 values
    let array_data = processor.to_arraydata(&encoded_snvs)?;
    
    println!("   Processed {} SNVs:", encoded_snvs.len());
    for (i, (position, u64_value)) in array_data.iter().enumerate().take(3) {
        println!("     SNV {}: position={}, u64={:016x}", i + 1, position, u64_value);
        
        // Decode to verify
        let decoded = UltraCompactSNV::decode_from_u64(*u64_value)?;
        println!("       Decoded: {:?}", decoded);
    }
    
    Ok(())
}

fn test_format_conversion() -> Result<()> {
    // Create an old-style UltraCompactSNV
    let old_format = UltraCompactSNV::encode_substitution_fast(b'C', b'G')?;
    
    // Convert to u64
    let u64_value = old_format.to_u64();
    println!("   Old format -> u64: {:016x}", u64_value);
    
    // Convert back to UltraCompactSNV
    let new_format = UltraCompactSNV::from_u64(u64_value);
    
    // Decode both to verify they're equivalent
    let old_decoded = old_format.decode_fast()?;
    let new_decoded = UltraCompactSNV::decode_from_u64(u64_value)?;
    
    println!("   Old decoded: {:?}", old_decoded);
    println!("   New decoded: {:?}", new_decoded);
    println!("   Equivalent: {}", old_decoded == new_decoded);
    
    Ok(())
}

fn demonstrate_performance_benefits() -> Result<()> {
    println!("   u64 encoding benefits:");
    println!("   - Memory: 8 bytes per mutation (was variable)");
    println!("   - Speed: Direct bit operations, no heap allocation");
    println!("   - Cache: Better cache locality with fixed size");
    println!("   - Vectorization: SIMD operations possible");
    
    // Show memory usage comparison
    let snv = SNV::new("chr1".to_string(), 1000, Mutation::Substitution(b'T'), 1);
    
    // Old format size (estimated)
    let old_format = UltraCompactSNV::encode_substitution_fast(b'A', b'T')?;
    let old_size = old_format.data.as_raw_slice().len();
    
    // New format size
    let new_size = std::mem::size_of::<u64>();
    
    println!("   Memory comparison:");
    println!("     Old format: ~{} bytes", old_size);
    println!("     New format: {} bytes", new_size);
    println!("     Savings: {}%", ((old_size as f64 - new_size as f64) / old_size as f64 * 100.0) as i32);
    
    Ok(())
}

// Helper functions for encoding (you should replace these with your actual implementation)

fn encode_substitution_to_u64(ref_base: u8, alt_base: u8) -> Result<u64> {
    let ref_bits = base_to_bits(ref_base)?;
    let alt_bits = base_to_bits(alt_base)?;
    
    // Pack: operation(2) | ref_base(2) | alt_base(2) | padding(58)
    let packed = (0b00u64 << 62) | ((ref_bits as u64) << 60) | ((alt_bits as u64) << 58);
    Ok(packed)
}

fn encode_deletion_to_u64(length: usize) -> Result<u64> {
    if length == 0 || length > 0x3FFFFFFF {
        anyhow::bail!("Invalid deletion length: {}", length);
    }
    
    // Pack: operation(2) | length(30) | padding(32)
    let packed = (0b01u64 << 62) | ((length as u64) << 32);
    Ok(packed)
}

fn encode_insertion_to_u64(bases: &[u8]) -> Result<u64> {
    if bases.is_empty() || bases.len() > 28 {
        anyhow::bail!("Invalid insertion length: {}", bases.len());
    }
    
    // Pack: operation(2) | length(6) | bases(2*length) | padding
    let mut packed = (0b10u64 << 62) | ((bases.len() as u64) << 56);
    
    for (i, &base) in bases.iter().enumerate() {
        let base_bits = base_to_bits(base)? as u64;
        packed |= base_bits << (54 - i * 2);
    }
    
    Ok(packed)
}

fn base_to_bits(base: u8) -> Result<u8> {
    match base.to_ascii_uppercase() {
        b'A' => Ok(0),
        b'T' => Ok(1),
        b'C' => Ok(2),
        b'G' => Ok(3),
        _ => anyhow::bail!("Invalid base: {}", base as char),
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_u64_encoding_roundtrip() -> Result<()> {
        // Test substitution roundtrip
        let original_sub = Mutation::Substitution(b'G');
        let encoded = encode_substitution_to_u64(b'A', b'G')?;
        let decoded = UltraCompactSNV::decode_from_u64(encoded)?;
        assert_eq!(original_sub, decoded);
        
        // Test deletion roundtrip
        let original_del = Mutation::Deletion(10);
        let encoded = encode_deletion_to_u64(10)?;
        let decoded = UltraCompactSNV::decode_from_u64(encoded)?;
        assert_eq!(original_del, decoded);
        
        // Test insertion roundtrip
        let original_ins = Mutation::Insertion(vec![b'A', b'T']);
        let encoded = encode_insertion_to_u64(&[b'A', b'T'])?;
        let decoded = UltraCompactSNV::decode_from_u64(encoded)?;
        assert_eq!(original_ins, decoded);
        
        Ok(())
    }
    
    #[test]
    fn test_encoded_snv_with_u64() -> Result<()> {
        let u64_value = encode_substitution_to_u64(b'C', b'T')?;
        
        let encoded_snv = EncodedSNV {
            chrom: "chr1",
            position: 1000,
            count: 2,
            compact_mutation: u64_value,
        };
        
        let decoded = encoded_snv.decode_mutation()?;
        assert_eq!(decoded, Mutation::Substitution(b'T'));
        
        let snv = encoded_snv.to_snv()?;
        assert_eq!(snv.chrom, "chr1");
        assert_eq!(snv.position, 1000);
        assert_eq!(snv.count, 2);
        assert_eq!(snv.mutation_type, Mutation::Substitution(b'T'));
        
        Ok(())
    }
}
