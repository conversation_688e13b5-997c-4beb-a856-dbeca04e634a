pub mod fragment;
pub mod utils;
pub mod snv;
pub mod quantification;
pub mod decode;
pub mod genome;

// Re-export commonly used types
pub use fragment::Fragment;
pub use fragment::CellBarcode;


#[cfg(test)]

#[test]
fn test_import_fragments() {
    use std::path::Path;
    use bed_utils::bed;
    let filename = "/data2/litian/precellar_data/processed_file/To_Nature_rna_fragment/test_RNA.tsv.zst";
    let fragment_file = Path::new(filename);
    let fragments = bed::io::Reader::new(
        utils::open_file_for_read(&fragment_file),
        Some("#".to_string()),
    )
    .into_records()
    .map(|f| {
        let f = f.unwrap();
        f
    })
    .collect::<Vec<Fragment>>();
    println!("{:?}", fragments[0]);
}