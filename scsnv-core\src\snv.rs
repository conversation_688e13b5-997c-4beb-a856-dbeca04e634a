use bincode::{Decode, Encode};
use crate::fragment::Fragment;
use anyhow::{Result,bail};
use lexical::parse_partial;


#[derive(Debug, Clone, PartialEq, Eq, Encode, Decode)]
pub enum Mutation {
    Deletion(u8), // The length of the deletion
    Insertion(Vec<u8>), // the inserted bases
    Substitution(u8), // The alternative base (A, T, C, G)
}

#[derive(Encode, Decode, Debug, Clone, PartialEq, Eq)]
pub struct SNV {
    pub chrom: String,
    pub position: u64, // position in the chromotin
    pub mutation_type: Mutation,
    pub count: u8,
}

impl SNV {
    pub fn new(chrom: String, position: u64, mutation_type: Mutation, count: u8) -> Self {
        Self { chrom, position, mutation_type, count }
    }
    pub fn form_mutation_str(&self) -> String {
        match &self.mutation_type {
            Mutation::Substitution(base) => format!("{}_{}:{}", self.chrom, self.position, *base as char),
            Mutation::Insertion(bases) => format!("{}_{}:+{}", self.chrom, self.position, bases.iter().map(|b| *b as char).collect::<String>()),
            Mutation::Deletion(length) => format!("{}_{}:-{}", self.chrom, self.position, length),
        }
    }
}   

#[derive(Encode, Decode, Debug, Clone)]
pub struct SNVs(Vec<SNV>,String); // snvs and barcode

impl SNVs {
    /// Create a new SNVs instance
    pub fn new(snvs: Vec<SNV>, barcode: String) -> Self {
        Self(snvs, barcode)
    }
    
    /// Get the SNVs vector
    pub fn snvs(&self) -> &Vec<SNV> {
        &self.0
    }
    
    /// Get the barcode
    pub fn barcode(&self) -> &str {
        &self.1
    }
    
    /// Consume and get the SNVs vector and barcode
    pub fn into_parts(self) -> (Vec<SNV>, String) {
        (self.0, self.1)
    }
}

impl TryFrom<Fragment> for SNVs {
    type Error = anyhow::Error;
    fn try_from(frag: Fragment) -> Result<Self> {
        Self::try_from(&frag)
    }
}
impl TryFrom<&Fragment> for SNVs {
    type Error = anyhow::Error;

    fn try_from(frag: &Fragment) -> Result<Self> {
        let barcode = frag.barcode.as_ref()
            .ok_or_else(|| anyhow::anyhow!("barcode is missing in fragment"))?
            .clone();
        let mut snvs = Vec::new();
        let chrom = frag.chrom.clone();
        let align1_start = frag.start1.unwrap();
        let rec_snv1 = frag.snv1.as_ref()
            .ok_or_else(|| anyhow::anyhow!("snv1 is missing in fragment"))?
            .as_bytes();
        
        let process_snv = |rec_snv: &[u8], align_start: u64| -> Result<Vec<SNV>> {
            let entries = parse_entries(rec_snv)?;
            let mut all_snvs = Vec::new();
            
            for entry in entries {
                let snv_arr: Vec<_> = parse_snv_str(entry.as_bytes())
                    .filter_map(|x| match x.unwrap() {
                        SNVKind::Match(0) => None,
                        k => Some(k),
                    })
                    .collect();
                
                let snvs = arr_to_snv(snv_arr, &chrom, align_start)?;
                all_snvs.extend(snvs);
            }
            
            Ok(all_snvs)
        };
        
        let snv1 = process_snv(rec_snv1, align1_start)?;
        snvs.extend(snv1);
        
        // Only process snv2 if both start2 and snv2 are present
        if let (Some(align2_start), Some(rec_snv2)) = (frag.start2, frag.snv2.as_ref()) {
            let snv2 = process_snv(rec_snv2.as_bytes(), align2_start)?;
            snvs.extend(snv2);
        }
        
        Ok(SNVs::new(snvs, barcode))
    }
}

#[derive(Debug, PartialEq, Eq)]
enum SNVKind {
    Match(u16),
    Substitution(u8),
    Deletion(u8),
    Insertion(Vec<u8>),
    Count(u8), // the count of fragments, only exists at the beginning of the array
}

impl SNVKind {
    fn from_str(s: &[u8]) -> Result<Option<(Self, &[u8])>> {
        if s.is_empty() {
            return Ok(None);
        }
        if s[0] == b'^' {
            // Deletion
            let l = s[1..].iter().take_while(|&&c| c == b'-').count();
            if l == 0 {
                bail!("Invalid SNV tag: Deletion without bases");
            } else {
                let unit: SNVKind = SNVKind::Deletion(l as u8);
                Ok(Some((unit, &s[1 + l..])))
            }
        } else if s[0] == b'+'{
            let l = s[1..].iter().take_while(|&&c| c.is_ascii_alphabetic()).count();
            if l == 0 {
                bail!("Invalid SNV tag: Insertion without bases");
            } else {
                let unit = SNVKind::Insertion(s[1..1 + l].to_vec());
                Ok(Some((unit, &s[1 + l..])))
            }
        }else if s[0].is_ascii_digit() {
            let (m, n) = parse_partial::<u16, _>(s)?;
            let unit = SNVKind::Match(m);
            Ok(Some((unit, &s[n..])))
        } else if s[0].is_ascii_alphabetic() {
            let unit = SNVKind::Substitution(s[0]);
            Ok(Some((unit, &s[1..])))
        } else if s[0] == b':' {
            let (c, n) = parse_partial::<u8, _>(&s[1..])?;
            let unit = SNVKind::Count(c);
            Ok(Some((unit, &s[n+1..])))
        } else {
            bail!("Invalid SNV tag: {}", String::from_utf8_lossy(s));
        }
    }
}

fn parse_snv_str(s: &[u8]) -> impl Iterator<Item = Result<SNVKind>> + '_{
    let mut s = s;
    std::iter::from_fn(move || {
        if s.is_empty() {
            return None;
        }
        match SNVKind::from_str(s) {
            Ok(Some((md, rest))) => {
                s = rest;
                Some(Ok(md))
            }
            Ok(None) => None,
            Err(e) => Some(Err(e)),
        }
    })
}

// If there are mutiple pieces in one fragment, they are separated by ';'
fn parse_entries(s: &[u8]) -> Result<Vec<&str>> {
    let input = std::str::from_utf8(s)?;
    let entries: Vec<&str> = if input.contains(';') {
        input.split(';').collect()
    } else {
        vec![input]
    };
    Ok(entries)
}

fn arr_to_snv(arr: Vec<SNVKind>, chrom: &str, start: u64) -> Result<Vec<SNV>> {
    let mut snvs = Vec::new();
    let mut position = start;

    // Extract count if present as the last element, otherwise default to 1
    let (arr, count) = if matches!(arr.last(), Some(SNVKind::Count(_))) {
        let count = if let Some(SNVKind::Count(c)) = arr.last() { *c } else { 1u8 };
        let mut arr = arr;
        arr.pop();
        (arr, count)
    } else {
        (arr, 1u8)
    };

    for unit in arr.into_iter() {
        match unit {
            SNVKind::Match(m) => {
                position += m as u64;
            }
            SNVKind::Substitution(b) => {
                snvs.push(SNV {
                    chrom: chrom.into(),
                    position,
                    mutation_type: Mutation::Substitution(b),
                    count,
                });
                position += 1;
            }
            SNVKind::Deletion(l) => {
                snvs.push(SNV {
                    chrom: chrom.into(),
                    position,
                    mutation_type: Mutation::Deletion(l as u8),
                    count,
                });
            }
            SNVKind::Insertion(bases) => {
                snvs.push(SNV {
                    chrom: chrom.into(),
                    position,
                    mutation_type: Mutation::Insertion(bases),
                    count,
                });
            }
            SNVKind::Count(_) => {
                bail!("Invalid SNV tag: Count found in the middle of the array");
            }
        }
    }
    Ok(snvs)
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_try_from_fragment() -> Result<()> {
        // Test basic fragment with SNVs in both reads
        let mut fragment = Fragment::new("chr1", 1000, 2000);
        fragment.start1 = Some(1000);
        fragment.start2 = Some(1500);
        fragment.snv1 = Some("5A20".to_string());
        fragment.snv2 = Some("10T30".to_string());

        let snvs = SNVs::try_from(&fragment)?;
        assert_eq!(snvs.0.len(), 2);

        // Check first SNV from read 1
        assert_eq!(snvs.0[0].chrom, "chr1");
        assert_eq!(snvs.0[0].position, 1005);
        assert_eq!(snvs.0[0].mutation_type, Mutation::Substitution(b'A'));
        assert_eq!(snvs.0[0].count, 1);

        // Check second SNV from read 2
        assert_eq!(snvs.0[1].chrom, "chr1");
        assert_eq!(snvs.0[1].position, 1510);
        assert_eq!(snvs.0[1].mutation_type, Mutation::Substitution(b'T'));
        assert_eq!(snvs.0[1].count, 1);

        Ok(())
    }

    #[test]
    fn test_try_from_fragment_with_complex_snvs() -> Result<()> {
        let mut fragment = Fragment::new("chr2", 2000, 3000);
        fragment.start1 = Some(2000);
        fragment.start2 = Some(2500);
        fragment.snv1 = Some("5A10^--15+CG20:2".to_string());
        fragment.snv2 = Some("8T12^---25:3".to_string());

        let snvs = SNVs::try_from(&fragment)?;
        assert_eq!(snvs.0.len(), 5);

        // From read 1 (count = 2)
        assert_eq!(snvs.0[0].position, 2005);
        assert_eq!(snvs.0[0].mutation_type, Mutation::Substitution(b'A'));
        assert_eq!(snvs.0[0].count, 2);

        assert_eq!(snvs.0[1].position, 2016);
        assert_eq!(snvs.0[1].mutation_type, Mutation::Deletion(2));
        assert_eq!(snvs.0[1].count, 2);

        assert_eq!(snvs.0[2].position, 2031);
        assert_eq!(snvs.0[2].mutation_type, Mutation::Insertion(vec![b'C', b'G']));
        assert_eq!(snvs.0[2].count, 2);

        // From read 2 (count = 3)
        assert_eq!(snvs.0[3].position, 2508);
        assert_eq!(snvs.0[3].mutation_type, Mutation::Substitution(b'T'));
        assert_eq!(snvs.0[3].count, 3);

        assert_eq!(snvs.0[4].position, 2521);
        assert_eq!(snvs.0[4].mutation_type, Mutation::Deletion(3));
        assert_eq!(snvs.0[4].count, 3);

        Ok(())
    }

    #[test]
    fn test_try_from_fragment_with_multiple_entries() -> Result<()> {
        let mut fragment = Fragment::new("chr3", 3000, 4000);
        fragment.start1 = Some(3000);
        fragment.start2 = Some(3500);
        fragment.snv1 = Some("5A20:1;10T15:2".to_string());
        fragment.snv2 = Some("8G25".to_string());

        let snvs = SNVs::try_from(&fragment)?;
        assert_eq!(snvs.0.len(), 3);

        // First entry from read 1
        assert_eq!(snvs.0[0].position, 3005);
        assert_eq!(snvs.0[0].mutation_type, Mutation::Substitution(b'A'));
        assert_eq!(snvs.0[0].count, 1);

        // Second entry from read 1
        assert_eq!(snvs.0[1].position, 3010);
        assert_eq!(snvs.0[1].mutation_type, Mutation::Substitution(b'T'));
        assert_eq!(snvs.0[1].count, 2);

        // From read 2
        assert_eq!(snvs.0[2].position, 3508);
        assert_eq!(snvs.0[2].mutation_type, Mutation::Substitution(b'G'));
        assert_eq!(snvs.0[2].count, 1);

        Ok(())
    }

    #[test]
    fn test_try_from_fragment_owned() -> Result<()> {
        let mut fragment = Fragment::new("chr4", 4000, 5000);
        fragment.start1 = Some(4000);
        fragment.start2 = Some(4500);
        fragment.snv1 = Some("10A30".to_string());
        fragment.snv2 = Some("20T40".to_string());

        // Test the owned version of TryFrom
        let snvs = SNVs::try_from(fragment)?;
        assert_eq!(snvs.0.len(), 2);

        assert_eq!(snvs.0[0].position, 4010);
        assert_eq!(snvs.0[0].mutation_type, Mutation::Substitution(b'A'));

        assert_eq!(snvs.0[1].position, 4520);
        assert_eq!(snvs.0[1].mutation_type, Mutation::Substitution(b'T'));

        Ok(())
    }

    #[test]
    fn test_try_from_fragment_missing_fields() {
        // Missing snv2
        let mut fragment = Fragment::new("chr5", 5000, 6000);
        fragment.start1 = Some(5000);
        fragment.snv1 = Some("10A30".to_string());

        let result = SNVs::try_from(&fragment);
        assert!(result.is_ok());
    }

    #[test]
    fn test_try_from_fragment_empty_snvs() -> Result<()> {
        let mut fragment = Fragment::new("chr6", 6000, 7000);
        fragment.start1 = Some(6000);
        fragment.start2 = Some(6500);
        fragment.snv1 = Some("50".to_string()); // Only matches, no mutations
        fragment.snv2 = Some("100".to_string()); // Only matches, no mutations

        let snvs = SNVs::try_from(&fragment)?;
        assert_eq!(snvs.0.len(), 0);

        Ok(())
    }

}